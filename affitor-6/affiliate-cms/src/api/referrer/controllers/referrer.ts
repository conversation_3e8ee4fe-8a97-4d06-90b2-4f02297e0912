/**
 * referrer controller
 */

import { factories } from '@strapi/strapi';

/**
 * Generate monthly performance data for the last 6 months
 */
async function generateMonthlyPerformanceData() {
  try {
    const now = new Date();

    // Generate date ranges for the last 12 months
    const monthRanges = Array.from({ length: 12 }, (_, i) => {
      const monthStart = new Date(now.getFullYear(), now.getMonth() - (11 - i), 1);
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - (11 - i) + 1, 0, 23, 59, 59, 999);
      return { monthStart, monthEnd };
    });

    // Run all database queries in parallel using Promise.all
    const monthlyData = await Promise.all(
      monthRanges.map(async ({ monthStart, monthEnd }) => {
        // Run all queries for this month in parallel
        const [monthCommissions, monthReferrals, monthLinks] = await Promise.all([
          // Get revenue for this month
          strapi.entityService.findMany('api::referral-commission.referral-commission', {
            filters: {
              createdAt: {
                $gte: monthStart.toISOString(),
                $lte: monthEnd.toISOString(),
              },
            },
            fields: ['gross_sale_amount'],
          }),

          // Get referrals count for this month
          strapi.entityService.count('api::referral.referral', {
            filters: {
              referral_status: 'conversion',
              createdAt: {
                $gte: monthStart.toISOString(),
                $lte: monthEnd.toISOString(),
              },
            },
          }),

          // Get clicks and leads for this month from referrer links
          strapi.entityService.findMany('api::referrer-link.referrer-link', {
            filters: {
              createdAt: {
                $gte: monthStart.toISOString(),
                $lte: monthEnd.toISOString(),
              },
            },
            fields: ['visitors', 'leads'],
          })
        ]);

        // Calculate totals
        const monthRevenue = monthCommissions.reduce((sum, commission) => {
          const amount = commission.gross_sale_amount;
          if (typeof amount === 'number') {
            return sum + amount;
          } else if (typeof amount === 'string') {
            return sum + (parseFloat(amount) || 0);
          }
          return sum;
        }, 0);

        const monthClicks = monthLinks.reduce((sum, link) => sum + (link.visitors || 0), 0);
        const monthLeads = monthLinks.reduce((sum, link) => sum + (link.leads || 0), 0);

        return {
          month: monthStart.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          revenue: parseFloat(monthRevenue.toFixed(2)),
          referrals: monthReferrals,
          clicks: monthClicks,
          leads: monthLeads,
        };
      })
    );

    return monthlyData;
  } catch (error) {
    strapi.log.error('Error generating monthly performance data:', error);
    return [];
  }
}

export default factories.createCoreController('api::referrer.referrer', ({ strapi }) => ({
  async register(ctx) {
    try {
      // Call the custom service method
      const result = await strapi.service('api::referrer.referrer').register({
        user: ctx.state.user,
      });

      return result;
    } catch (err) {
      ctx.body = err;
      return ctx.badRequest('Registration failed', { error: err.message });
    }
  },

  async dashboard(ctx) {
    try {
      // Get total count of referrers in the system
      const totalReferrers = await strapi.entityService.count('api::referrer.referrer');

      // Get total count of all referrals in the system with referral_status 'conversion'
      const totalReferrals = await strapi.entityService.count('api::referral.referral', {
        filters: { referral_status: 'conversion' },
      });

      // Get all referrer links to calculate total clicks and leads
      const allReferrerLinks = await strapi.entityService.findMany(
        'api::referrer-link.referrer-link',
        {
          fields: ['visitors', 'leads', 'conversions'],
        }
      );

      // Calculate total clicks (visitors) and total leads from all referrer links
      const totalClicks = allReferrerLinks.reduce((sum, link) => sum + (link.visitors || 0), 0);
      const totalLeads = allReferrerLinks.reduce((sum, link) => sum + (link.leads || 0), 0);

      // Get total revenue from all referral commissions in the system
      const allReferralCommissions = await strapi.entityService.findMany(
        'api::referral-commission.referral-commission',
        {
          fields: ['gross_sale_amount', 'createdAt'],
        }
      );

      const totalRevenue = allReferralCommissions.reduce((sum, commission) => {
        const amount = commission.gross_sale_amount;
        if (typeof amount === 'number') {
          return sum + amount;
        } else if (typeof amount === 'string') {
          return sum + (parseFloat(amount) || 0);
        }
        return sum;
      }, 0);

      const recentActivity = await strapi.entityService.findMany(
        'api::referral-activity.referral-activity',
        {
          sort: { createdAt: 'desc' },
          limit: 10,
          populate: {
            referral: {
              populate: {
                user: true,
              },
            },
          },
        }
      );

      // Calculate monthly revenue statistics
      const now = new Date();
      const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      const previousMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const previousMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59, 999);

      // Get current month revenue
      const currentMonthCommissions = await strapi.entityService.findMany(
        'api::referral-commission.referral-commission',
        {
          filters: {
            createdAt: {
              $gte: currentMonthStart.toISOString(),
            },
          },
          fields: ['gross_sale_amount'],
        }
      );

      const currentMonthRevenue = currentMonthCommissions.reduce((sum, commission) => {
        const amount = commission.gross_sale_amount;
        if (typeof amount === 'number') {
          return sum + amount;
        } else if (typeof amount === 'string') {
          return sum + (parseFloat(amount) || 0);
        }
        return sum;
      }, 0);

      // Get previous month revenue
      const previousMonthCommissions = await strapi.entityService.findMany(
        'api::referral-commission.referral-commission',
        {
          filters: {
            createdAt: {
              $gte: previousMonthStart.toISOString(),
              $lte: previousMonthEnd.toISOString(),
            },
          },
          fields: ['gross_sale_amount'],
        }
      );

      const previousMonthRevenueAmount = previousMonthCommissions.reduce((sum, commission) => {
        const amount = commission.gross_sale_amount;
        if (typeof amount === 'number') {
          return sum + amount;
        } else if (typeof amount === 'string') {
          return sum + (parseFloat(amount) || 0);
        }
        return sum;
      }, 0);

      const growthRate =
        previousMonthRevenueAmount > 0
          ? ((currentMonthRevenue - previousMonthRevenueAmount) / previousMonthRevenueAmount) * 100
          : currentMonthRevenue > 0
            ? 100
            : 0;

      const topReferrers = await strapi.entityService.findMany('api::referrer.referrer', {
        sort: { total_earnings: 'desc' },
        limit: 10,
        populate: {
          user: {
            fields: ['username', 'email'],
          },
        },
      });

      const topReferrals = await strapi.entityService.findMany('api::referral.referral', {
        sort: { createdAt: 'desc' },
        limit: 10,
        populate: {
          user: {
            fields: ['username', 'email'],
          },
        },
      });

      // Generate monthly performance overview data for the last 6 months
      const monthlyPerformanceData = await generateMonthlyPerformanceData();

      return {
        success: true,
        data: {
          totalReferrers,
          totalReferrals,
          totalClicks,
          totalLeads,
          totalRevenue: parseFloat(totalRevenue.toFixed(2)),
          recentActivity,
          topReferrers,
          topReferrals,
          previousMonthRevenueAmount: parseFloat(previousMonthRevenueAmount.toFixed(2)),
          currentMonthRevenue: parseFloat(currentMonthRevenue.toFixed(2)),
          growthRate: parseFloat(growthRate.toFixed(2)),
          monthlyPerformance: monthlyPerformanceData,
        },
      };
    } catch (err) {
      ctx.body = err;
      return ctx.badRequest('Dashboard data retrieval failed', { error: err.message });
    }
  },

  /**
   * Sync referrer financial data
   * POST /api/referrers/sync-data
   * Body: { referrerId?: number, all?: boolean }
   */
  async syncData(ctx) {
    try {
      const { referrerId, all } = ctx.request.body || {};
      const referrerService = strapi.service('api::referrer.referrer');

      // Single referrer sync
      if (referrerId) {
        const result = await referrerService.recalculateReferrerMetrics(referrerId);
        return ctx.send({
          success: true,
          message: `Data synced successfully for referrer ${referrerId}`,
          data: {
            referrerId,
            previousTotals: result.previousTotals,
            updatedTotals: result.updatedTotals,
            commissionsProcessed: result.calculationDetails.commissionsProcessed,
          },
        });
      }

      // Bulk sync all referrers
      if (all) {
        const result = await referrerService.bulkRecalculateMetrics();
        return ctx.send({
          success: true,
          message: result.message,
          data: {
            processed: result.processed,
            successfulUpdates: result.summary.successfulUpdates,
            failedUpdates: result.summary.failedUpdates,
            totalRevenueUpdated: result.summary.totalRevenueUpdated,
            totalEarningsUpdated: result.summary.totalEarningsUpdated,
            errors: result.errors,
          },
        });
      }

      return ctx.badRequest('Please provide either referrerId for single sync or all: true for bulk sync');
    } catch (err) {
      strapi.log.error('Error in syncData:', err);
      return ctx.badRequest('Failed to sync referrer data', {
        error: err.message,
        timestamp: new Date().toISOString(),
      });
    }
  },
}));
